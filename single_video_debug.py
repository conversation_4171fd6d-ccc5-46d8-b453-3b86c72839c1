#!/usr/bin/env python3
"""
单视频深度调试工具
逐步分析关键帧检测失败的原因
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
from scipy.signal import find_peaks
from scipy.ndimage import gaussian_filter1d
import argparse

class SingleVideoDebugger:
    def __init__(self, video_path):
        """单视频调试器"""
        self.video_path = video_path
        self.frames = []
        self.frame_diffs = []
        self.optical_flows = []
        
    def load_video_frames(self, max_frames=100):
        """加载视频帧"""
        print(f"加载视频: {os.path.basename(self.video_path)}")
        
        cap = cv2.VideoCapture(self.video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频: {self.video_path}")
        
        frame_count = 0
        while frame_count < max_frames:
            ret, frame = cap.read()
            if not ret:
                break
            self.frames.append(frame)
            frame_count += 1
        
        cap.release()
        print(f"加载了 {len(self.frames)} 帧")
        
    def compute_frame_differences_detailed(self):
        """详细计算帧差"""
        print("计算帧差...")
        
        self.frame_diffs = []
        prev_gray = None
        
        for i, frame in enumerate(self.frames):
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            if prev_gray is not None:
                # 多种帧差计算方法
                diff_abs = cv2.absdiff(prev_gray, gray)
                diff_norm = np.mean(diff_abs) / 255.0
                
                # 额外的差异度量
                diff_std = np.std(diff_abs.astype(np.float32)) / 255.0
                diff_max = np.max(diff_abs) / 255.0
                
                self.frame_diffs.append({
                    'frame_idx': i,
                    'mean_diff': diff_norm,
                    'std_diff': diff_std,
                    'max_diff': diff_max,
                    'total_diff': np.sum(diff_abs.astype(np.float32)) / (255.0 * diff_abs.size)
                })
                
                print(f"帧 {i}: 均值差={diff_norm:.4f}, 标准差={diff_std:.4f}, 最大值={diff_max:.4f}")
            
            prev_gray = gray
        
        print(f"计算了 {len(self.frame_diffs)} 个帧差")
        
    def compute_optical_flow_detailed(self):
        """详细计算光流"""
        print("计算光流...")
        
        self.optical_flows = []
        
        for i in range(len(self.frames) - 1):
            gray1 = cv2.cvtColor(self.frames[i], cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(self.frames[i + 1], cv2.COLOR_BGR2GRAY)
            
            try:
                # 方法1: 稀疏光流 (Lucas-Kanade)
                # 检测角点
                corners = cv2.goodFeaturesToTrack(gray1, maxCorners=100, qualityLevel=0.01, minDistance=10)
                
                if corners is not None and len(corners) > 0:
                    # 计算光流
                    next_pts, status, error = cv2.calcOpticalFlowPyrLK(gray1, gray2, corners, None)
                    
                    # 选择好的点
                    good_new = next_pts[status == 1]
                    good_old = corners[status == 1]
                    
                    if len(good_new) > 0 and len(good_old) > 0:
                        # 计算运动向量
                        motion_vectors = good_new - good_old
                        motion_magnitudes = np.sqrt(motion_vectors[:, 0]**2 + motion_vectors[:, 1]**2)
                        
                        sparse_flow_mean = np.mean(motion_magnitudes)
                        sparse_flow_max = np.max(motion_magnitudes)
                        sparse_flow_std = np.std(motion_magnitudes)
                    else:
                        sparse_flow_mean = sparse_flow_max = sparse_flow_std = 0.0
                else:
                    sparse_flow_mean = sparse_flow_max = sparse_flow_std = 0.0
                
                # 方法2: 稠密光流 (Farneback)
                try:
                    flow = cv2.calcOpticalFlowPyrLK(gray1, gray2, None, None)
                    if flow is not None:
                        magnitude = np.sqrt(flow[:, :, 0]**2 + flow[:, :, 1]**2)
                        dense_flow_mean = np.mean(magnitude)
                        dense_flow_max = np.max(magnitude)
                        dense_flow_std = np.std(magnitude)
                    else:
                        dense_flow_mean = dense_flow_max = dense_flow_std = 0.0
                except:
                    dense_flow_mean = dense_flow_max = dense_flow_std = 0.0
                
                self.optical_flows.append({
                    'frame_idx': i + 1,
                    'sparse_mean': sparse_flow_mean,
                    'sparse_max': sparse_flow_max,
                    'sparse_std': sparse_flow_std,
                    'dense_mean': dense_flow_mean,
                    'dense_max': dense_flow_max,
                    'dense_std': dense_flow_std,
                    'corner_count': len(corners) if corners is not None else 0
                })
                
                print(f"帧 {i+1}: 稀疏光流={sparse_flow_mean:.2f}, 稠密光流={dense_flow_mean:.2f}, 角点数={len(corners) if corners is not None else 0}")
                
            except Exception as e:
                print(f"光流计算错误 (帧 {i+1}): {e}")
                self.optical_flows.append({
                    'frame_idx': i + 1,
                    'sparse_mean': 0.0,
                    'sparse_max': 0.0,
                    'sparse_std': 0.0,
                    'dense_mean': 0.0,
                    'dense_max': 0.0,
                    'dense_std': 0.0,
                    'corner_count': 0
                })
        
        print(f"计算了 {len(self.optical_flows)} 个光流")
    
    def test_peak_detection_variants(self):
        """测试不同的峰值检测参数"""
        print("\n=== 测试峰值检测参数 ===")
        
        if not self.frame_diffs:
            print("没有帧差数据")
            return
        
        # 提取帧差均值
        mean_diffs = np.array([fd['mean_diff'] for fd in self.frame_diffs])
        
        # 测试不同的参数组合
        test_configs = [
            {'height': 0.005, 'prominence': 0.001, 'distance': 1, 'sigma': 0.5},
            {'height': 0.01, 'prominence': 0.005, 'distance': 2, 'sigma': 1.0},
            {'height': 0.015, 'prominence': 0.01, 'distance': 3, 'sigma': 1.5},
            {'height': 0.02, 'prominence': 0.015, 'distance': 3, 'sigma': 2.0},
        ]
        
        results = []
        
        for i, config in enumerate(test_configs):
            # 高斯平滑
            smoothed = gaussian_filter1d(mean_diffs, sigma=config['sigma'])
            
            # 峰值检测
            peaks, properties = find_peaks(
                smoothed,
                height=config['height'],
                prominence=config['prominence'],
                distance=config['distance']
            )
            
            result = {
                'config': config,
                'peaks': peaks,
                'peak_count': len(peaks),
                'peak_values': smoothed[peaks] if len(peaks) > 0 else []
            }
            results.append(result)
            
            print(f"配置 {i+1}: height={config['height']:.3f}, prominence={config['prominence']:.3f}, "
                  f"distance={config['distance']}, sigma={config['sigma']:.1f}")
            print(f"  检测到 {len(peaks)} 个峰值: {peaks.tolist()}")
            if len(peaks) > 0:
                print(f"  峰值强度: {[f'{v:.4f}' for v in smoothed[peaks]]}")
            print()
        
        return results
    
    def test_optical_flow_thresholds(self):
        """测试不同的光流阈值"""
        print("\n=== 测试光流阈值 ===")
        
        if not self.optical_flows:
            print("没有光流数据")
            return
        
        # 提取不同类型的光流数据
        sparse_means = np.array([of['sparse_mean'] for of in self.optical_flows])
        dense_means = np.array([of['dense_mean'] for of in self.optical_flows])
        
        thresholds = [0.1, 0.5, 1.0, 1.5, 2.0, 3.0]
        
        print("光流阈值测试结果:")
        print("阈值 | 稀疏光流通过 | 稠密光流通过")
        print("-" * 40)
        
        for threshold in thresholds:
            sparse_pass = np.sum(sparse_means > threshold)
            dense_pass = np.sum(dense_means > threshold)
            
            print(f"{threshold:5.1f} | {sparse_pass:12d} | {dense_pass:12d}")
        
        return {
            'sparse_means': sparse_means,
            'dense_means': dense_means,
            'thresholds': thresholds
        }
    
    def visualize_detailed_analysis(self, save_path=None):
        """详细可视化分析结果"""
        if not self.frame_diffs or not self.optical_flows:
            print("缺少分析数据")
            return
        
        fig, axes = plt.subplots(3, 2, figsize=(15, 12))
        
        # 提取数据
        frame_indices = [fd['frame_idx'] for fd in self.frame_diffs]
        mean_diffs = [fd['mean_diff'] for fd in self.frame_diffs]
        std_diffs = [fd['std_diff'] for fd in self.frame_diffs]
        max_diffs = [fd['max_diff'] for fd in self.frame_diffs]
        
        sparse_flows = [of['sparse_mean'] for of in self.optical_flows]
        dense_flows = [of['dense_mean'] for of in self.optical_flows]
        corner_counts = [of['corner_count'] for of in self.optical_flows]
        
        # 1. 帧差均值
        axes[0, 0].plot(frame_indices, mean_diffs, 'b-', alpha=0.7, label='Mean Diff')
        axes[0, 0].axhline(y=0.0161, color='r', linestyle='--', label='推荐阈值')
        axes[0, 0].axhline(y=0.01, color='g', linestyle=':', label='更低阈值')
        axes[0, 0].set_title('帧差均值')
        axes[0, 0].set_ylabel('帧差值')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 帧差标准差和最大值
        axes[0, 1].plot(frame_indices, std_diffs, 'orange', alpha=0.7, label='Std Diff')
        axes[0, 1].plot(frame_indices, max_diffs, 'red', alpha=0.7, label='Max Diff')
        axes[0, 1].set_title('帧差标准差和最大值')
        axes[0, 1].set_ylabel('帧差值')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 稀疏光流
        flow_indices = [of['frame_idx'] for of in self.optical_flows]
        axes[1, 0].plot(flow_indices, sparse_flows, 'g-', alpha=0.7, label='Sparse Flow')
        axes[1, 0].axhline(y=2.03, color='r', linestyle='--', label='推荐阈值')
        axes[1, 0].axhline(y=0.5, color='orange', linestyle=':', label='更低阈值')
        axes[1, 0].set_title('稀疏光流')
        axes[1, 0].set_ylabel('光流幅度')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 稠密光流
        axes[1, 1].plot(flow_indices, dense_flows, 'purple', alpha=0.7, label='Dense Flow')
        axes[1, 1].axhline(y=2.03, color='r', linestyle='--', label='推荐阈值')
        axes[1, 1].axhline(y=0.5, color='orange', linestyle=':', label='更低阈值')
        axes[1, 1].set_title('稠密光流')
        axes[1, 1].set_ylabel('光流幅度')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        # 5. 角点数量
        axes[2, 0].plot(flow_indices, corner_counts, 'brown', alpha=0.7, label='Corner Count')
        axes[2, 0].set_title('检测到的角点数量')
        axes[2, 0].set_ylabel('角点数')
        axes[2, 0].set_xlabel('帧索引')
        axes[2, 0].legend()
        axes[2, 0].grid(True, alpha=0.3)
        
        # 6. 帧差分布
        axes[2, 1].hist(mean_diffs, bins=20, alpha=0.7, edgecolor='black')
        axes[2, 1].axvline(x=0.0161, color='r', linestyle='--', label='推荐阈值')
        axes[2, 1].axvline(x=0.01, color='g', linestyle=':', label='更低阈值')
        axes[2, 1].set_title('帧差分布')
        axes[2, 1].set_xlabel('帧差值')
        axes[2, 1].set_ylabel('频次')
        axes[2, 1].legend()
        axes[2, 1].grid(True, alpha=0.3)
        
        plt.suptitle(f'详细分析: {os.path.basename(self.video_path)}', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"详细分析图保存到: {save_path}")
        else:
            plt.show()
        
        plt.close()
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print(f"\n{'='*60}")
        print(f"开始深度分析视频: {os.path.basename(self.video_path)}")
        print(f"{'='*60}")
        
        # 1. 加载视频
        self.load_video_frames(max_frames=100)
        
        # 2. 计算帧差
        self.compute_frame_differences_detailed()
        
        # 3. 计算光流
        self.compute_optical_flow_detailed()
        
        # 4. 测试峰值检测
        peak_results = self.test_peak_detection_variants()
        
        # 5. 测试光流阈值
        flow_results = self.test_optical_flow_thresholds()
        
        # 6. 可视化
        self.visualize_detailed_analysis(f'detailed_analysis_{os.path.basename(self.video_path)}.png')
        
        # 7. 给出建议
        self.provide_recommendations(peak_results, flow_results)
        
        return {
            'peak_results': peak_results,
            'flow_results': flow_results,
            'frame_diffs': self.frame_diffs,
            'optical_flows': self.optical_flows
        }
    
    def provide_recommendations(self, peak_results, flow_results):
        """提供优化建议"""
        print(f"\n{'='*60}")
        print("优化建议:")
        print(f"{'='*60}")
        
        # 找到检测到最多峰值的配置
        best_peak_config = max(peak_results, key=lambda x: x['peak_count'])
        
        print(f"1. 推荐峰值检测参数:")
        print(f"   --frame_diff_threshold {best_peak_config['config']['height']:.4f}")
        print(f"   --prominence {best_peak_config['config']['prominence']:.4f}")
        print(f"   --distance {best_peak_config['config']['distance']}")
        print(f"   --gaussian_sigma {best_peak_config['config']['sigma']:.1f}")
        print(f"   (可检测到 {best_peak_config['peak_count']} 个峰值)")
        
        # 光流建议
        sparse_means = flow_results['sparse_means']
        dense_means = flow_results['dense_means']
        
        sparse_75th = np.percentile(sparse_means, 75) if len(sparse_means) > 0 else 0
        dense_75th = np.percentile(dense_means, 75) if len(dense_means) > 0 else 0
        
        print(f"\n2. 推荐光流阈值:")
        print(f"   稀疏光流75%分位数: {sparse_75th:.2f}")
        print(f"   稠密光流75%分位数: {dense_75th:.2f}")
        print(f"   建议使用: {min(sparse_75th, dense_75th, 1.0):.2f}")
        
        print(f"\n3. 备选策略:")
        print(f"   如果仍然检测不到关键帧，建议:")
        print(f"   - 使用纯帧差方法 (--method frame_diff)")
        print(f"   - 进一步降低阈值到 0.005")
        print(f"   - 使用固定间隔作为后备 (--method fixed)")

def main():
    parser = argparse.ArgumentParser(description='单视频深度调试')
    parser.add_argument('--video_path', type=str, required=True, help='视频文件路径')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.video_path):
        print(f"视频文件不存在: {args.video_path}")
        return
    
    debugger = SingleVideoDebugger(args.video_path)
    results = debugger.run_complete_analysis()
    
    print(f"\n分析完成！检查生成的图像文件了解详细结果。")

if __name__ == "__main__":
    main()
