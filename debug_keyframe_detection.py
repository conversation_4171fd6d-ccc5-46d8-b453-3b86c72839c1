#!/usr/bin/env python3
"""
关键帧检测调试工具
分析视频特征并优化阈值参数
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
import glob
from pathlib import Path
from tqdm import tqdm
import json
from scipy.signal import find_peaks
from scipy.ndimage import gaussian_filter1d

class KeyFrameDebugger:
    def __init__(self):
        """关键帧检测调试器"""
        pass
    
    def analyze_single_video(self, video_path, max_frames=100):
        """分析单个视频的特征"""
        print(f"\n=== 分析视频: {os.path.basename(video_path)} ===")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"无法打开视频: {video_path}")
            return None
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"视频信息: {width}x{height}, {fps:.1f}fps, {total_frames}帧")
        
        frames = []
        frame_diffs = []
        optical_flows = []
        prev_gray = None
        
        frame_count = 0
        while frame_count < min(max_frames, total_frames):
            ret, frame = cap.read()
            if not ret:
                break
            
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            frames.append(frame)
            
            if prev_gray is not None:
                # 计算帧差
                diff = cv2.absdiff(prev_gray, gray)
                diff_norm = np.mean(diff) / 255.0
                frame_diffs.append(diff_norm)
                
                # 计算光流（简化版本）
                try:
                    flow = cv2.calcOpticalFlowPyrLK(
                        prev_gray, gray, 
                        np.array([[width//4, height//4], [3*width//4, height//4], 
                                 [width//4, 3*height//4], [3*width//4, 3*height//4]], dtype=np.float32),
                        None
                    )
                    if flow[0] is not None and len(flow[0]) > 0:
                        # 计算运动幅度
                        motion = np.sqrt(np.sum((flow[0] - np.array([[width//4, height//4], [3*width//4, height//4], 
                                                                    [width//4, 3*height//4], [3*width//4, 3*height//4]]))**2, axis=1))
                        optical_flows.append(np.mean(motion))
                    else:
                        optical_flows.append(0.0)
                except:
                    optical_flows.append(0.0)
            
            prev_gray = gray
            frame_count += 1
        
        cap.release()
        
        if not frame_diffs:
            print("未能计算帧差")
            return None
        
        frame_diffs = np.array(frame_diffs)
        optical_flows = np.array(optical_flows)
        
        # 统计信息
        stats = {
            'video_path': video_path,
            'total_frames': len(frames),
            'frame_diffs': {
                'min': np.min(frame_diffs),
                'max': np.max(frame_diffs),
                'mean': np.mean(frame_diffs),
                'std': np.std(frame_diffs),
                'percentiles': {
                    '50': np.percentile(frame_diffs, 50),
                    '75': np.percentile(frame_diffs, 75),
                    '90': np.percentile(frame_diffs, 90),
                    '95': np.percentile(frame_diffs, 95)
                }
            },
            'optical_flows': {
                'min': np.min(optical_flows),
                'max': np.max(optical_flows),
                'mean': np.mean(optical_flows),
                'std': np.std(optical_flows),
                'percentiles': {
                    '50': np.percentile(optical_flows, 50),
                    '75': np.percentile(optical_flows, 75),
                    '90': np.percentile(optical_flows, 90),
                    '95': np.percentile(optical_flows, 95)
                }
            },
            'raw_data': {
                'frame_diffs': frame_diffs.tolist(),
                'optical_flows': optical_flows.tolist()
            }
        }
        
        print(f"帧差统计:")
        print(f"  范围: {stats['frame_diffs']['min']:.4f} - {stats['frame_diffs']['max']:.4f}")
        print(f"  均值: {stats['frame_diffs']['mean']:.4f} ± {stats['frame_diffs']['std']:.4f}")
        print(f"  95%分位数: {stats['frame_diffs']['percentiles']['95']:.4f}")
        
        print(f"光流统计:")
        print(f"  范围: {stats['optical_flows']['min']:.2f} - {stats['optical_flows']['max']:.2f}")
        print(f"  均值: {stats['optical_flows']['mean']:.2f} ± {stats['optical_flows']['std']:.2f}")
        print(f"  95%分位数: {stats['optical_flows']['percentiles']['95']:.2f}")
        
        return stats
    
    def test_threshold_combinations(self, video_stats, frame_diff_thresholds, optical_flow_thresholds):
        """测试不同阈值组合的效果"""
        print(f"\n=== 测试阈值组合 ===")
        
        frame_diffs = np.array(video_stats['raw_data']['frame_diffs'])
        optical_flows = np.array(video_stats['raw_data']['optical_flows'])
        
        results = []
        
        for fd_thresh in frame_diff_thresholds:
            for of_thresh in optical_flow_thresholds:
                # 模拟关键帧检测过程
                
                # 1. 帧差峰值检测
                smoothed_diffs = gaussian_filter1d(frame_diffs, sigma=1.0)
                peaks, _ = find_peaks(smoothed_diffs, height=fd_thresh, prominence=0.01, distance=3)
                
                # 2. 光流验证
                validated_keyframes = []
                for peak in peaks:
                    if peak < len(optical_flows) and optical_flows[peak] > of_thresh:
                        validated_keyframes.append(peak)
                
                # 3. 生成帧对
                frame_pairs = len(validated_keyframes) - 1 if len(validated_keyframes) > 1 else 0
                
                results.append({
                    'frame_diff_threshold': fd_thresh,
                    'optical_flow_threshold': of_thresh,
                    'detected_peaks': len(peaks),
                    'validated_keyframes': len(validated_keyframes),
                    'frame_pairs': frame_pairs,
                    'coverage_ratio': len(validated_keyframes) / len(frame_diffs) if len(frame_diffs) > 0 else 0
                })
        
        # 排序结果
        results.sort(key=lambda x: x['frame_pairs'], reverse=True)
        
        print("阈值测试结果 (按帧对数量排序):")
        print("帧差阈值 | 光流阈值 | 检测峰值 | 验证关键帧 | 帧对数 | 覆盖率")
        print("-" * 70)
        
        for result in results[:10]:  # 显示前10个结果
            print(f"{result['frame_diff_threshold']:8.3f} | {result['optical_flow_threshold']:8.1f} | "
                  f"{result['detected_peaks']:8d} | {result['validated_keyframes']:10d} | "
                  f"{result['frame_pairs']:6d} | {result['coverage_ratio']:7.1%}")
        
        return results
    
    def visualize_video_analysis(self, video_stats, recommended_thresholds, save_path=None):
        """可视化视频分析结果"""
        frame_diffs = np.array(video_stats['raw_data']['frame_diffs'])
        optical_flows = np.array(video_stats['raw_data']['optical_flows'])
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 帧差时序图
        axes[0, 0].plot(frame_diffs, alpha=0.7, label='Frame Differences')
        axes[0, 0].axhline(y=recommended_thresholds['frame_diff'], color='r', linestyle='--', 
                          label=f'推荐阈值: {recommended_thresholds["frame_diff"]:.3f}')
        axes[0, 0].axhline(y=0.05, color='orange', linestyle=':', alpha=0.7, label='原阈值: 0.05')
        axes[0, 0].set_title('帧差时序分析')
        axes[0, 0].set_xlabel('帧索引')
        axes[0, 0].set_ylabel('帧差值')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 光流时序图
        axes[0, 1].plot(optical_flows, alpha=0.7, color='green', label='Optical Flow')
        axes[0, 1].axhline(y=recommended_thresholds['optical_flow'], color='r', linestyle='--',
                          label=f'推荐阈值: {recommended_thresholds["optical_flow"]:.1f}')
        axes[0, 1].axhline(y=2.0, color='orange', linestyle=':', alpha=0.7, label='原阈值: 2.0')
        axes[0, 1].set_title('光流时序分析')
        axes[0, 1].set_xlabel('帧索引')
        axes[0, 1].set_ylabel('光流幅度')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 帧差分布直方图
        axes[1, 0].hist(frame_diffs, bins=30, alpha=0.7, edgecolor='black')
        axes[1, 0].axvline(x=recommended_thresholds['frame_diff'], color='r', linestyle='--',
                          label=f'推荐阈值: {recommended_thresholds["frame_diff"]:.3f}')
        axes[1, 0].axvline(x=0.05, color='orange', linestyle=':', alpha=0.7, label='原阈值: 0.05')
        axes[1, 0].set_title('帧差分布')
        axes[1, 0].set_xlabel('帧差值')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 光流分布直方图
        axes[1, 1].hist(optical_flows, bins=30, alpha=0.7, color='green', edgecolor='black')
        axes[1, 1].axvline(x=recommended_thresholds['optical_flow'], color='r', linestyle='--',
                          label=f'推荐阈值: {recommended_thresholds["optical_flow"]:.1f}')
        axes[1, 1].axvline(x=2.0, color='orange', linestyle=':', alpha=0.7, label='原阈值: 2.0')
        axes[1, 1].set_title('光流分布')
        axes[1, 1].set_xlabel('光流幅度')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.suptitle(f'视频分析: {os.path.basename(video_stats["video_path"])}', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"可视化结果保存到: {save_path}")
        else:
            plt.show()
        
        plt.close()
    
    def recommend_thresholds(self, video_stats_list):
        """基于多个视频统计推荐阈值"""
        print(f"\n=== 基于 {len(video_stats_list)} 个视频推荐阈值 ===")
        
        all_frame_diffs = []
        all_optical_flows = []
        
        for stats in video_stats_list:
            all_frame_diffs.extend(stats['raw_data']['frame_diffs'])
            all_optical_flows.extend(stats['raw_data']['optical_flows'])
        
        all_frame_diffs = np.array(all_frame_diffs)
        all_optical_flows = np.array(all_optical_flows)
        
        # 推荐策略：使用较低的百分位数来捕获细微变化
        recommended_frame_diff = np.percentile(all_frame_diffs, 75)  # 75%分位数
        recommended_optical_flow = np.percentile(all_optical_flows, 70)  # 70%分位数
        
        # 确保阈值不会太低
        recommended_frame_diff = max(recommended_frame_diff, 0.01)
        recommended_optical_flow = max(recommended_optical_flow, 0.5)
        
        print(f"推荐帧差阈值: {recommended_frame_diff:.4f} (原值: 0.05)")
        print(f"推荐光流阈值: {recommended_optical_flow:.2f} (原值: 2.0)")
        
        return {
            'frame_diff': recommended_frame_diff,
            'optical_flow': recommended_optical_flow,
            'statistics': {
                'frame_diff_percentiles': {
                    '50': np.percentile(all_frame_diffs, 50),
                    '75': np.percentile(all_frame_diffs, 75),
                    '90': np.percentile(all_frame_diffs, 90),
                    '95': np.percentile(all_frame_diffs, 95)
                },
                'optical_flow_percentiles': {
                    '50': np.percentile(all_optical_flows, 50),
                    '70': np.percentile(all_optical_flows, 70),
                    '80': np.percentile(all_optical_flows, 80),
                    '90': np.percentile(all_optical_flows, 90)
                }
            }
        }

def main():
    """主函数：分析视频并推荐阈值"""
    # 查找测试视频
    video_dirs = [
        'data/short_videos/segmented_videos_ok',
        'data/short_videos/segmented_videos_nok_apperence', 
        'data/short_videos/segmented_videos_nok_ele'
    ]
    
    test_videos = []
    for video_dir in video_dirs:
        if os.path.exists(video_dir):
            videos = glob.glob(os.path.join(video_dir, '*.mp4'))[:3]  # 每个目录取3个视频
            test_videos.extend(videos)
    
    if not test_videos:
        print("未找到测试视频文件")
        return
    
    print(f"找到 {len(test_videos)} 个测试视频")
    
    # 创建调试器
    debugger = KeyFrameDebugger()
    
    # 分析视频
    video_stats_list = []
    for video_path in test_videos[:5]:  # 分析前5个视频
        stats = debugger.analyze_single_video(video_path, max_frames=50)
        if stats:
            video_stats_list.append(stats)
    
    if not video_stats_list:
        print("未能分析任何视频")
        return
    
    # 推荐阈值
    recommendations = debugger.recommend_thresholds(video_stats_list)
    
    # 测试第一个视频的阈值组合
    if video_stats_list:
        print(f"\n=== 测试视频: {os.path.basename(video_stats_list[0]['video_path'])} ===")
        
        # 测试不同阈值组合
        frame_diff_thresholds = [0.01, 0.02, 0.03, 0.04, 0.05]
        optical_flow_thresholds = [0.5, 1.0, 1.5, 2.0, 2.5]
        
        threshold_results = debugger.test_threshold_combinations(
            video_stats_list[0], frame_diff_thresholds, optical_flow_thresholds
        )
        
        # 可视化结果
        debugger.visualize_video_analysis(
            video_stats_list[0], 
            recommendations,
            'video_analysis_debug.png'
        )
    
    # 保存推荐结果
    with open('recommended_thresholds.json', 'w') as f:
        json.dump(recommendations, f, indent=2)
    
    print(f"\n推荐阈值已保存到: recommended_thresholds.json")
    print(f"建议使用以下参数重新运行智能预处理器:")
    print(f"--frame_diff_threshold {recommendations['frame_diff']:.4f}")
    print(f"--optical_flow_threshold {recommendations['optical_flow']:.2f}")

if __name__ == "__main__":
    main()
