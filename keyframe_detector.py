#!/usr/bin/env python3
"""
智能关键帧检测器
基于光流、帧差和峰值检测的混合方法
"""

import cv2
import numpy as np
from scipy.signal import find_peaks
from scipy.ndimage import gaussian_filter1d
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional
import os
from tqdm import tqdm

class KeyFrameDetector:
    def __init__(self, 
                 frame_diff_threshold: float = 0.05,
                 optical_flow_threshold: float = 2.0,
                 min_interval: int = 3,
                 max_interval: int = 30,
                 peak_prominence: float = 0.02,
                 gaussian_sigma: float = 1.0):
        """
        智能关键帧检测器
        
        Args:
            frame_diff_threshold: 帧差阈值
            optical_flow_threshold: 光流运动幅度阈值
            min_interval: 最小帧间隔
            max_interval: 最大帧间隔
            peak_prominence: 峰值检测的显著性阈值
            gaussian_sigma: 高斯平滑参数
        """
        self.frame_diff_threshold = frame_diff_threshold
        self.optical_flow_threshold = optical_flow_threshold
        self.min_interval = min_interval
        self.max_interval = max_interval
        self.peak_prominence = peak_prominence
        self.gaussian_sigma = gaussian_sigma
        
    def compute_frame_differences(self, video_path: str) -> Tuple[np.ndarray, List[np.ndarray]]:
        """计算视频帧间差异"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        frames = []
        frame_diffs = []
        prev_gray = None
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            frames.append(frame)
            
            if prev_gray is not None:
                # 计算帧差
                diff = cv2.absdiff(prev_gray, gray)
                diff_norm = np.mean(diff) / 255.0
                frame_diffs.append(diff_norm)
            
            prev_gray = gray
        
        cap.release()
        return np.array(frame_diffs), frames
    
    def compute_optical_flow_magnitude(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
        """计算两帧间的光流运动幅度"""
        gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY) if len(frame1.shape) == 3 else frame1
        gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY) if len(frame2.shape) == 3 else frame2
        
        # 使用Farneback光流算法
        flow = cv2.calcOpticalFlowPyrLK(gray1, gray2, None, None)
        
        # 计算运动幅度
        if flow[0] is not None and len(flow[0]) > 0:
            magnitude = np.sqrt(flow[0][:, :, 0]**2 + flow[0][:, :, 1]**2)
            return np.mean(magnitude)
        else:
            # 使用稠密光流作为备选
            flow = cv2.calcOpticalFlowPyrLK(gray1, gray2, None, None)
            if flow is not None:
                magnitude = np.sqrt(flow[:, :, 0]**2 + flow[:, :, 1]**2)
                return np.mean(magnitude)
        
        return 0.0
    
    def detect_motion_peaks(self, frame_diffs: np.ndarray) -> List[int]:
        """检测运动峰值"""
        # 高斯平滑
        smoothed_diffs = gaussian_filter1d(frame_diffs, sigma=self.gaussian_sigma)
        
        # 峰值检测
        peaks, properties = find_peaks(
            smoothed_diffs,
            height=self.frame_diff_threshold,
            prominence=self.peak_prominence,
            distance=self.min_interval
        )
        
        return peaks.tolist()
    
    def apply_temporal_constraints(self, keyframe_indices: List[int], total_frames: int) -> List[int]:
        """应用时间约束，确保关键帧分布合理"""
        if not keyframe_indices:
            return []
        
        # 排序
        keyframe_indices = sorted(keyframe_indices)
        
        # 应用最小间隔约束
        filtered_keyframes = [keyframe_indices[0]]
        
        for i in range(1, len(keyframe_indices)):
            if keyframe_indices[i] - filtered_keyframes[-1] >= self.min_interval:
                filtered_keyframes.append(keyframe_indices[i])
        
        # 应用最大间隔约束，确保没有太长的空白期
        final_keyframes = []
        last_keyframe = 0
        
        for keyframe in filtered_keyframes:
            # 如果间隔太大，插入中间帧
            if keyframe - last_keyframe > self.max_interval:
                # 在中间插入关键帧
                mid_frame = last_keyframe + self.max_interval
                while mid_frame < keyframe:
                    final_keyframes.append(mid_frame)
                    mid_frame += self.max_interval
            
            final_keyframes.append(keyframe)
            last_keyframe = keyframe
        
        # 确保最后一段也不会太长
        if total_frames - last_keyframe > self.max_interval:
            mid_frame = last_keyframe + self.max_interval
            while mid_frame < total_frames - 1:
                final_keyframes.append(mid_frame)
                mid_frame += self.max_interval
        
        return final_keyframes
    
    def detect_keyframes(self, video_path: str, method: str = 'hybrid') -> List[int]:
        """
        检测关键帧
        
        Args:
            video_path: 视频文件路径
            method: 检测方法 ('frame_diff', 'optical_flow', 'hybrid')
        
        Returns:
            关键帧索引列表
        """
        print(f"正在分析视频: {os.path.basename(video_path)}")
        
        # 计算帧差
        frame_diffs, frames = self.compute_frame_differences(video_path)
        
        if method == 'frame_diff':
            # 仅使用帧差方法
            keyframes = self.detect_motion_peaks(frame_diffs)
            
        elif method == 'optical_flow':
            # 仅使用光流方法（计算密集，仅用于对比）
            keyframes = []
            for i in tqdm(range(len(frames) - 1), desc="计算光流"):
                flow_magnitude = self.compute_optical_flow_magnitude(frames[i], frames[i+1])
                if flow_magnitude > self.optical_flow_threshold:
                    keyframes.append(i)
                    
        elif method == 'hybrid':
            # 混合方法：帧差检测 + 光流验证
            candidate_keyframes = self.detect_motion_peaks(frame_diffs)
            
            keyframes = []
            print("验证候选关键帧...")
            for idx in tqdm(candidate_keyframes, desc="光流验证"):
                if idx < len(frames) - 1:
                    flow_magnitude = self.compute_optical_flow_magnitude(frames[idx], frames[idx+1])
                    if flow_magnitude > self.optical_flow_threshold:
                        keyframes.append(idx)
        
        else:
            raise ValueError(f"不支持的检测方法: {method}")
        
        # 应用时间约束
        keyframes = self.apply_temporal_constraints(keyframes, len(frames))
        
        print(f"检测到 {len(keyframes)} 个关键帧 (总帧数: {len(frames)})")
        return keyframes
    
    def extract_keyframe_pairs(self, video_path: str, method: str = 'hybrid') -> List[Tuple[int, int]]:
        """提取关键帧对"""
        keyframes = self.detect_keyframes(video_path, method)
        
        # 生成帧对
        frame_pairs = []
        for i in range(len(keyframes) - 1):
            frame_pairs.append((keyframes[i], keyframes[i + 1]))
        
        return frame_pairs
    
    def visualize_detection_results(self, video_path: str, save_path: Optional[str] = None):
        """可视化检测结果"""
        frame_diffs, frames = self.compute_frame_differences(video_path)
        keyframes = self.detect_motion_peaks(frame_diffs)
        
        plt.figure(figsize=(15, 8))
        
        # 绘制帧差曲线
        plt.subplot(2, 1, 1)
        plt.plot(frame_diffs, label='Frame Differences', alpha=0.7)
        plt.axhline(y=self.frame_diff_threshold, color='r', linestyle='--', label='Threshold')
        
        # 标记检测到的关键帧
        if keyframes:
            plt.scatter(keyframes, frame_diffs[keyframes], color='red', s=50, zorder=5, label='Detected Keyframes')
        
        plt.xlabel('Frame Index')
        plt.ylabel('Frame Difference')
        plt.title(f'关键帧检测结果 - {os.path.basename(video_path)}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 绘制关键帧分布直方图
        plt.subplot(2, 1, 2)
        if keyframes:
            intervals = np.diff(keyframes)
            plt.hist(intervals, bins=20, alpha=0.7, edgecolor='black')
            plt.axvline(x=self.min_interval, color='r', linestyle='--', label=f'Min Interval ({self.min_interval})')
            plt.axvline(x=self.max_interval, color='g', linestyle='--', label=f'Max Interval ({self.max_interval})')
        
        plt.xlabel('Frame Interval')
        plt.ylabel('Frequency')
        plt.title('关键帧间隔分布')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"可视化结果已保存到: {save_path}")
        else:
            plt.show()
        
        plt.close()

def main():
    """测试关键帧检测器"""
    # 示例用法
    detector = KeyFrameDetector(
        frame_diff_threshold=0.05,
        optical_flow_threshold=2.0,
        min_interval=3,
        max_interval=30
    )
    
    # 测试视频路径（请替换为实际路径）
    test_video = "data/short_videos/segmented_videos_ok/example.mp4"
    
    if os.path.exists(test_video):
        # 检测关键帧
        keyframes = detector.detect_keyframes(test_video, method='hybrid')
        print(f"检测到的关键帧: {keyframes}")
        
        # 生成帧对
        frame_pairs = detector.extract_keyframe_pairs(test_video)
        print(f"生成的帧对数量: {len(frame_pairs)}")
        
        # 可视化结果
        detector.visualize_detection_results(test_video, "keyframe_detection_result.png")
    else:
        print(f"测试视频文件不存在: {test_video}")

if __name__ == "__main__":
    main()
