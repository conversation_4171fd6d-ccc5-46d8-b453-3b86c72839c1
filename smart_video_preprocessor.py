#!/usr/bin/env python3
"""
智能视频预处理器
集成关键帧检测的LAQ数据预处理器
"""

import os
import cv2
import numpy as np
import torch
from PIL import Image
import json
import argparse
from pathlib import Path
from tqdm import tqdm
import glob
from torchvision import transforms as T
from keyframe_detector import KeyFrameDetector

class SmartVideoPreprocessor:
    def __init__(self, 
                 target_size=(256, 256),
                 keyframe_method='hybrid',
                 frame_diff_threshold=0.05,
                 optical_flow_threshold=2.0,
                 min_interval=3,
                 max_interval=30):
        """
        智能视频预处理器
        
        Args:
            target_size: 目标分辨率
            keyframe_method: 关键帧检测方法 ('fixed', 'frame_diff', 'optical_flow', 'hybrid')
            frame_diff_threshold: 帧差阈值
            optical_flow_threshold: 光流阈值
            min_interval: 最小帧间隔
            max_interval: 最大帧间隔
        """
        self.target_size = target_size
        self.keyframe_method = keyframe_method
        
        # 初始化关键帧检测器
        if keyframe_method != 'fixed':
            self.keyframe_detector = KeyFrameDetector(
                frame_diff_threshold=frame_diff_threshold,
                optical_flow_threshold=optical_flow_threshold,
                min_interval=min_interval,
                max_interval=max_interval
            )
        
        # LAQ模型期望的图像变换
        self.transform = T.Compose([
            T.Lambda(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
            T.Resize(target_size),
            T.ToTensor(),
        ])
    
    def analyze_video(self, video_path):
        """分析视频属性"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return None
            
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': duration,
            'aspect_ratio': width / height if height > 0 else 1.0
        }
    
    def extract_frames_fixed_interval(self, video_path, output_dir, frame_step=5, max_frames=100):
        """固定间隔提取帧（原始方法）"""
        video_info = self.analyze_video(video_path)
        if video_info is None:
            print(f"无法打开视频: {video_path}")
            return []
        
        cap = cv2.VideoCapture(video_path)
        
        video_name = Path(video_path).stem
        video_output_dir = os.path.join(output_dir, video_name)
        os.makedirs(video_output_dir, exist_ok=True)
        
        frame_pairs = []
        frame_idx = 0
        pair_idx = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            # 转换BGR到RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 保存当前帧
            current_frame_path = os.path.join(video_output_dir, f"img_{frame_idx:05d}.jpg")
            Image.fromarray(frame_rgb).save(current_frame_path)
            
            # 创建帧对
            if frame_idx >= frame_step:
                first_frame_path = os.path.join(video_output_dir, f"img_{frame_idx-frame_step:05d}.jpg")
                second_frame_path = current_frame_path
                
                if os.path.exists(first_frame_path):
                    frame_pairs.append({
                        'video_name': video_name,
                        'pair_idx': pair_idx,
                        'first_frame': first_frame_path,
                        'second_frame': second_frame_path,
                        'frame_indices': [frame_idx-frame_step, frame_idx],
                        'method': 'fixed_interval'
                    })
                    pair_idx += 1
            
            frame_idx += 1
            
            if max_frames and frame_idx >= max_frames:
                break
        
        cap.release()
        return frame_pairs
    
    def extract_frames_keyframe_based(self, video_path, output_dir):
        """基于关键帧检测提取帧对"""
        video_info = self.analyze_video(video_path)
        if video_info is None:
            print(f"无法打开视频: {video_path}")
            return []
        
        # 检测关键帧对
        try:
            keyframe_pairs = self.keyframe_detector.extract_keyframe_pairs(video_path, self.keyframe_method)
        except Exception as e:
            print(f"关键帧检测失败: {e}")
            return []
        
        if not keyframe_pairs:
            print(f"未检测到关键帧对: {video_path}")
            return []
        
        # 提取关键帧
        cap = cv2.VideoCapture(video_path)
        video_name = Path(video_path).stem
        video_output_dir = os.path.join(output_dir, video_name)
        os.makedirs(video_output_dir, exist_ok=True)
        
        # 读取所有帧
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        cap.release()
        
        frame_pairs = []
        for pair_idx, (first_idx, second_idx) in enumerate(keyframe_pairs):
            if first_idx < len(frames) and second_idx < len(frames):
                # 保存第一帧
                first_frame_path = os.path.join(video_output_dir, f"keyframe_{first_idx:05d}.jpg")
                Image.fromarray(frames[first_idx]).save(first_frame_path)
                
                # 保存第二帧
                second_frame_path = os.path.join(video_output_dir, f"keyframe_{second_idx:05d}.jpg")
                Image.fromarray(frames[second_idx]).save(second_frame_path)
                
                frame_pairs.append({
                    'video_name': video_name,
                    'pair_idx': pair_idx,
                    'first_frame': first_frame_path,
                    'second_frame': second_frame_path,
                    'frame_indices': [first_idx, second_idx],
                    'method': self.keyframe_method,
                    'frame_interval': second_idx - first_idx
                })
        
        return frame_pairs
    
    def process_video_directory(self, input_dir, output_dir):
        """处理整个视频目录"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 查找所有视频文件
        video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv']
        video_files = []
        
        for ext in video_extensions:
            video_files.extend(glob.glob(os.path.join(input_dir, '**', ext), recursive=True))
        
        print(f"找到 {len(video_files)} 个视频文件")
        print(f"使用方法: {self.keyframe_method}")
        
        all_frame_pairs = []
        processed_videos = []
        
        for video_path in tqdm(video_files, desc="处理视频"):
            try:
                # 分析视频
                video_info = self.analyze_video(video_path)
                if video_info is None:
                    continue
                
                # 根据方法选择提取策略
                if self.keyframe_method == 'fixed':
                    frame_pairs = self.extract_frames_fixed_interval(video_path, output_dir)
                else:
                    frame_pairs = self.extract_frames_keyframe_based(video_path, output_dir)
                
                all_frame_pairs.extend(frame_pairs)
                
                processed_videos.append({
                    'video_path': video_path,
                    'video_info': video_info,
                    'frame_pairs_count': len(frame_pairs),
                    'method': self.keyframe_method
                })
                
                print(f"处理完成: {os.path.basename(video_path)} - {len(frame_pairs)} 帧对")
                
            except Exception as e:
                print(f"处理视频时出错 {video_path}: {e}")
                continue
        
        # 保存处理结果的元数据
        metadata = {
            'processed_videos': processed_videos,
            'total_frame_pairs': len(all_frame_pairs),
            'preprocessing_config': {
                'target_size': self.target_size,
                'keyframe_method': self.keyframe_method,
                'frame_diff_threshold': getattr(self, 'keyframe_detector', {}).frame_diff_threshold if hasattr(self, 'keyframe_detector') else None,
                'optical_flow_threshold': getattr(self, 'keyframe_detector', {}).optical_flow_threshold if hasattr(self, 'keyframe_detector') else None,
            }
        }
        
        metadata_path = os.path.join(output_dir, f'preprocessing_metadata_{self.keyframe_method}.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        # 保存帧对信息
        frame_pairs_path = os.path.join(output_dir, f'frame_pairs_{self.keyframe_method}.json')
        with open(frame_pairs_path, 'w', encoding='utf-8') as f:
            json.dump(all_frame_pairs, f, indent=2, ensure_ascii=False)
        
        return all_frame_pairs, metadata
    
    def create_laq_dataset_jsonl(self, frame_pairs, output_path):
        """创建LAQ推理所需的JSONL格式数据"""
        with open(output_path, 'w', encoding='utf-8') as f:
            for pair in frame_pairs:
                entry = {
                    'id': f"{pair['video_name']}_{pair['pair_idx']:05d}",
                    'image': pair['first_frame'],
                    'next_image': pair['second_frame'],
                    'instruction': "Predict the action between these two frames",
                    'vision': "",
                    'fields': "[instruction],[vision],delta",
                    'method': pair['method'],
                    'frame_indices': pair['frame_indices']
                }
                
                # 添加额外信息（如果有）
                if 'frame_interval' in pair:
                    entry['frame_interval'] = pair['frame_interval']
                
                f.write(json.dumps(entry, ensure_ascii=False) + '\n')
        
        print(f"创建JSONL数据集: {output_path}")
        return output_path

def main():
    parser = argparse.ArgumentParser(description='智能视频数据预处理器')
    parser.add_argument('--input_dir', type=str, required=True, help='输入视频目录')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--method', type=str, default='hybrid', 
                       choices=['fixed', 'frame_diff', 'optical_flow', 'hybrid'],
                       help='帧采样方法')
    parser.add_argument('--target_size', type=int, nargs=2, default=[256, 256], 
                       help='目标分辨率 [width height]')
    parser.add_argument('--frame_diff_threshold', type=float, default=0.05, 
                       help='帧差阈值')
    parser.add_argument('--optical_flow_threshold', type=float, default=2.0, 
                       help='光流阈值')
    parser.add_argument('--min_interval', type=int, default=3, help='最小帧间隔')
    parser.add_argument('--max_interval', type=int, default=30, help='最大帧间隔')
    
    args = parser.parse_args()
    
    # 创建智能预处理器
    preprocessor = SmartVideoPreprocessor(
        target_size=tuple(args.target_size),
        keyframe_method=args.method,
        frame_diff_threshold=args.frame_diff_threshold,
        optical_flow_threshold=args.optical_flow_threshold,
        min_interval=args.min_interval,
        max_interval=args.max_interval
    )
    
    # 处理视频
    frame_pairs, metadata = preprocessor.process_video_directory(args.input_dir, args.output_dir)
    
    # 创建JSONL数据集
    jsonl_path = os.path.join(args.output_dir, f'laq_dataset_{args.method}.jsonl')
    preprocessor.create_laq_dataset_jsonl(frame_pairs, jsonl_path)
    
    print(f"\n智能预处理完成!")
    print(f"方法: {args.method}")
    print(f"总共处理了 {len(metadata['processed_videos'])} 个视频")
    print(f"生成了 {len(frame_pairs)} 个帧对")
    print(f"输出目录: {args.output_dir}")
    print(f"JSONL数据集: {jsonl_path}")

if __name__ == "__main__":
    main()
