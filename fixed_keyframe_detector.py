#!/usr/bin/env python3
"""
修复版关键帧检测器
解决光流计算和阈值设置问题
"""

import cv2
import numpy as np
from scipy.signal import find_peaks
from scipy.ndimage import gaussian_filter1d
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional
import os
from tqdm import tqdm

class FixedKeyFrameDetector:
    def __init__(self, 
                 frame_diff_threshold: float = 0.005,  # 大幅降低
                 optical_flow_threshold: float = 0.3,   # 大幅降低
                 min_interval: int = 3,
                 max_interval: int = 30,
                 peak_prominence: float = 0.001,        # 大幅降低
                 gaussian_sigma: float = 0.5,           # 减少平滑
                 use_adaptive_threshold: bool = True):   # 新增自适应阈值
        """
        修复版关键帧检测器
        
        Args:
            frame_diff_threshold: 帧差阈值（大幅降低）
            optical_flow_threshold: 光流阈值（大幅降低）
            min_interval: 最小帧间隔
            max_interval: 最大帧间隔
            peak_prominence: 峰值显著性阈值（大幅降低）
            gaussian_sigma: 高斯平滑参数（减少平滑）
            use_adaptive_threshold: 是否使用自适应阈值
        """
        self.frame_diff_threshold = frame_diff_threshold
        self.optical_flow_threshold = optical_flow_threshold
        self.min_interval = min_interval
        self.max_interval = max_interval
        self.peak_prominence = peak_prominence
        self.gaussian_sigma = gaussian_sigma
        self.use_adaptive_threshold = use_adaptive_threshold
        
    def compute_frame_differences(self, video_path: str) -> Tuple[np.ndarray, List[np.ndarray]]:
        """计算视频帧间差异"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        frames = []
        frame_diffs = []
        prev_gray = None
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            frames.append(frame)
            
            if prev_gray is not None:
                # 计算帧差
                diff = cv2.absdiff(prev_gray, gray)
                diff_norm = np.mean(diff) / 255.0
                frame_diffs.append(diff_norm)
            
            prev_gray = gray
        
        cap.release()
        return np.array(frame_diffs), frames
    
    def compute_optical_flow_magnitude_fixed(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
        """修复版光流计算"""
        gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY) if len(frame1.shape) == 3 else frame1
        gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY) if len(frame2.shape) == 3 else frame2
        
        try:
            # 方法1: 使用Lucas-Kanade稀疏光流
            # 检测角点
            corners = cv2.goodFeaturesToTrack(
                gray1, 
                maxCorners=100, 
                qualityLevel=0.01, 
                minDistance=10,
                blockSize=3
            )
            
            if corners is not None and len(corners) > 0:
                # 计算光流
                next_pts, status, error = cv2.calcOpticalFlowPyrLK(
                    gray1, gray2, corners, None,
                    winSize=(15, 15),
                    maxLevel=2,
                    criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)
                )
                
                # 选择好的点
                good_new = next_pts[status == 1]
                good_old = corners[status == 1]
                
                if len(good_new) > 0 and len(good_old) > 0:
                    # 计算运动向量
                    motion_vectors = good_new - good_old
                    motion_magnitudes = np.sqrt(motion_vectors[:, 0]**2 + motion_vectors[:, 1]**2)
                    return np.mean(motion_magnitudes)
            
            # 方法2: 如果稀疏光流失败，使用简单的块匹配
            # 将图像分成小块，计算块间的位移
            h, w = gray1.shape
            block_size = 16
            displacements = []
            
            for y in range(0, h - block_size, block_size):
                for x in range(0, w - block_size, block_size):
                    block1 = gray1[y:y+block_size, x:x+block_size]
                    
                    # 在第二帧中搜索最佳匹配
                    search_range = 8
                    min_diff = float('inf')
                    best_displacement = 0
                    
                    for dy in range(-search_range, search_range + 1):
                        for dx in range(-search_range, search_range + 1):
                            ny, nx = y + dy, x + dx
                            if 0 <= ny < h - block_size and 0 <= nx < w - block_size:
                                block2 = gray2[ny:ny+block_size, nx:nx+block_size]
                                diff = np.sum(np.abs(block1.astype(np.float32) - block2.astype(np.float32)))
                                if diff < min_diff:
                                    min_diff = diff
                                    best_displacement = np.sqrt(dy**2 + dx**2)
                    
                    displacements.append(best_displacement)
            
            return np.mean(displacements) if displacements else 0.0
            
        except Exception as e:
            print(f"光流计算错误: {e}")
            return 0.0
    
    def detect_motion_peaks_adaptive(self, frame_diffs: np.ndarray) -> List[int]:
        """自适应峰值检测"""
        if self.use_adaptive_threshold:
            # 自适应阈值：使用数据的统计特性
            adaptive_threshold = max(
                np.percentile(frame_diffs, 60),  # 60%分位数
                self.frame_diff_threshold        # 最小阈值
            )
            adaptive_prominence = max(
                np.std(frame_diffs) * 0.5,      # 标准差的一半
                self.peak_prominence             # 最小显著性
            )
        else:
            adaptive_threshold = self.frame_diff_threshold
            adaptive_prominence = self.peak_prominence
        
        # 高斯平滑
        smoothed_diffs = gaussian_filter1d(frame_diffs, sigma=self.gaussian_sigma)
        
        # 峰值检测
        peaks, properties = find_peaks(
            smoothed_diffs,
            height=adaptive_threshold,
            prominence=adaptive_prominence,
            distance=self.min_interval
        )
        
        print(f"自适应阈值: {adaptive_threshold:.4f}, 显著性: {adaptive_prominence:.4f}")
        print(f"检测到 {len(peaks)} 个峰值")
        
        return peaks.tolist()
    
    def apply_temporal_constraints(self, keyframe_indices: List[int], total_frames: int) -> List[int]:
        """应用时间约束，确保关键帧分布合理"""
        if not keyframe_indices:
            # 如果没有检测到关键帧，使用固定间隔作为后备
            print("未检测到关键帧，使用固定间隔后备策略")
            backup_keyframes = list(range(0, total_frames, self.max_interval // 2))
            return backup_keyframes[:min(len(backup_keyframes), total_frames // 3)]
        
        # 排序
        keyframe_indices = sorted(keyframe_indices)
        
        # 应用最小间隔约束
        filtered_keyframes = [keyframe_indices[0]]
        
        for i in range(1, len(keyframe_indices)):
            if keyframe_indices[i] - filtered_keyframes[-1] >= self.min_interval:
                filtered_keyframes.append(keyframe_indices[i])
        
        # 应用最大间隔约束
        final_keyframes = []
        last_keyframe = 0
        
        for keyframe in filtered_keyframes:
            # 如果间隔太大，插入中间帧
            if keyframe - last_keyframe > self.max_interval:
                mid_frame = last_keyframe + self.max_interval
                while mid_frame < keyframe:
                    final_keyframes.append(mid_frame)
                    mid_frame += self.max_interval
            
            final_keyframes.append(keyframe)
            last_keyframe = keyframe
        
        # 确保最后一段也不会太长
        if total_frames - last_keyframe > self.max_interval:
            mid_frame = last_keyframe + self.max_interval
            while mid_frame < total_frames - 1:
                final_keyframes.append(mid_frame)
                mid_frame += self.max_interval
        
        return final_keyframes
    
    def detect_keyframes(self, video_path: str, method: str = 'adaptive_hybrid') -> List[int]:
        """
        检测关键帧
        
        Args:
            video_path: 视频文件路径
            method: 检测方法 ('frame_diff', 'adaptive_hybrid', 'fixed_interval')
        
        Returns:
            关键帧索引列表
        """
        print(f"正在分析视频: {os.path.basename(video_path)}")
        
        # 计算帧差
        frame_diffs, frames = self.compute_frame_differences(video_path)
        
        if method == 'frame_diff':
            # 仅使用帧差方法
            keyframes = self.detect_motion_peaks_adaptive(frame_diffs)
            
        elif method == 'adaptive_hybrid':
            # 自适应混合方法
            candidate_keyframes = self.detect_motion_peaks_adaptive(frame_diffs)
            
            if not candidate_keyframes:
                print("帧差检测未找到候选关键帧，使用固定间隔")
                keyframes = list(range(0, len(frames), 10))  # 每10帧一个
            else:
                keyframes = []
                print("验证候选关键帧...")
                
                # 使用更宽松的光流验证
                for idx in tqdm(candidate_keyframes, desc="光流验证"):
                    if idx < len(frames) - 1:
                        flow_magnitude = self.compute_optical_flow_magnitude_fixed(frames[idx], frames[idx+1])
                        
                        # 自适应光流阈值
                        if self.use_adaptive_threshold:
                            # 对于工业视频，使用更低的光流阈值
                            adaptive_flow_threshold = min(self.optical_flow_threshold, 0.5)
                        else:
                            adaptive_flow_threshold = self.optical_flow_threshold
                        
                        if flow_magnitude > adaptive_flow_threshold:
                            keyframes.append(idx)
                        else:
                            # 即使光流验证失败，如果帧差足够大，也保留
                            if idx < len(frame_diffs) and frame_diffs[idx] > self.frame_diff_threshold * 2:
                                keyframes.append(idx)
                                print(f"保留高帧差关键帧: {idx} (帧差: {frame_diffs[idx]:.4f})")
                
                # 如果光流验证后没有关键帧，降级到纯帧差方法
                if not keyframes:
                    print("光流验证全部失败，使用纯帧差方法")
                    keyframes = candidate_keyframes
                    
        elif method == 'fixed_interval':
            # 固定间隔方法（后备）
            keyframes = list(range(0, len(frames), 8))  # 每8帧一个
            
        else:
            raise ValueError(f"不支持的检测方法: {method}")
        
        # 应用时间约束
        keyframes = self.apply_temporal_constraints(keyframes, len(frames))
        
        print(f"最终检测到 {len(keyframes)} 个关键帧 (总帧数: {len(frames)})")
        return keyframes
    
    def extract_keyframe_pairs(self, video_path: str, method: str = 'adaptive_hybrid') -> List[Tuple[int, int]]:
        """提取关键帧对"""
        keyframes = self.detect_keyframes(video_path, method)
        
        # 生成帧对
        frame_pairs = []
        for i in range(len(keyframes) - 1):
            frame_pairs.append((keyframes[i], keyframes[i + 1]))
        
        # 如果帧对太少，添加一些固定间隔的帧对
        if len(frame_pairs) < 3:
            print(f"帧对数量太少 ({len(frame_pairs)})，添加固定间隔帧对")
            cap = cv2.VideoCapture(video_path)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            cap.release()
            
            # 添加固定间隔帧对
            interval = max(5, total_frames // 10)  # 至少5帧间隔
            for i in range(0, total_frames - interval, interval):
                frame_pairs.append((i, i + interval))
        
        return frame_pairs

def main():
    """测试修复版关键帧检测器"""
    # 测试视频路径
    test_video = "data/short_videos/segmented_videos_ok/20250728T075409Z_20250728T075909Z_decrypted_roi-00.00.02.101-00.00.03.453-seg01.mp4"
    
    if os.path.exists(test_video):
        # 创建修复版检测器
        detector = FixedKeyFrameDetector(
            frame_diff_threshold=0.005,
            optical_flow_threshold=0.3,
            min_interval=3,
            max_interval=20,
            use_adaptive_threshold=True
        )
        
        # 测试不同方法
        methods = ['frame_diff', 'adaptive_hybrid', 'fixed_interval']
        
        for method in methods:
            print(f"\n{'='*50}")
            print(f"测试方法: {method}")
            print(f"{'='*50}")
            
            keyframes = detector.detect_keyframes(test_video, method)
            frame_pairs = detector.extract_keyframe_pairs(test_video, method)
            
            print(f"关键帧: {keyframes}")
            print(f"帧对数量: {len(frame_pairs)}")
            print(f"帧对: {frame_pairs[:5]}...")  # 显示前5个
    else:
        print(f"测试视频文件不存在: {test_video}")

if __name__ == "__main__":
    main()
