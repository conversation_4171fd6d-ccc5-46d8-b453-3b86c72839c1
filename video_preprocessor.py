#!/usr/bin/env python3
"""
LAQ视频数据预处理器
将原始视频数据转换为LAQ模型可接受的格式
"""

import os
import cv2
import numpy as np
import torch
from PIL import Image
import json
import argparse
from pathlib import Path
from tqdm import tqdm
import glob
from torchvision import transforms as T

class LAQVideoPreprocessor:
    def __init__(self, 
                 target_size=(256, 256),
                 target_fps=25.0,
                 max_frames=None,
                 frame_step=5):
        """
        LAQ视频预处理器
        
        Args:
            target_size: 目标分辨率 (width, height)
            target_fps: 目标帧率
            max_frames: 最大帧数限制
            frame_step: 帧间隔（用于LAQ的时序建模）
        """
        self.target_size = target_size
        self.target_fps = target_fps
        self.max_frames = max_frames
        self.frame_step = frame_step
        
        # LAQ模型期望的图像变换
        self.transform = T.Compose([
            T.Lambda(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
            T.<PERSON>si<PERSON>(target_size),
            T.To<PERSON>ensor(),
        ])
    
    def analyze_video(self, video_path):
        """分析视频属性"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return None
            
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': duration,
            'aspect_ratio': width / height if height > 0 else 1.0
        }
    
    def extract_frame_pairs(self, video_path, output_dir):
        """
        从视频中提取帧对，符合LAQ的输入要求
        LAQ期望输入为两帧的序列：[batch, channels, 2, height, width]
        """
        video_info = self.analyze_video(video_path)
        if video_info is None:
            print(f"无法打开视频: {video_path}")
            return []
        
        cap = cv2.VideoCapture(video_path)
        
        # 创建输出目录
        video_name = Path(video_path).stem
        video_output_dir = os.path.join(output_dir, video_name)
        os.makedirs(video_output_dir, exist_ok=True)
        
        frame_pairs = []
        frame_idx = 0
        pair_idx = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            # 转换BGR到RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 保存当前帧
            current_frame_path = os.path.join(video_output_dir, f"img_{frame_idx:05d}.jpg")
            Image.fromarray(frame_rgb).save(current_frame_path)
            
            # 如果有足够的帧来创建帧对
            if frame_idx >= self.frame_step:
                first_frame_path = os.path.join(video_output_dir, f"img_{frame_idx-self.frame_step:05d}.jpg")
                second_frame_path = current_frame_path
                
                if os.path.exists(first_frame_path):
                    frame_pairs.append({
                        'video_name': video_name,
                        'pair_idx': pair_idx,
                        'first_frame': first_frame_path,
                        'second_frame': second_frame_path,
                        'frame_indices': [frame_idx-self.frame_step, frame_idx]
                    })
                    pair_idx += 1
            
            frame_idx += 1
            
            # 限制最大帧数
            if self.max_frames and frame_idx >= self.max_frames:
                break
        
        cap.release()
        return frame_pairs
    
    def create_laq_tensor(self, first_frame_path, second_frame_path):
        """
        创建LAQ模型期望的输入张量
        返回形状为 [channels, 2, height, width] 的张量
        """
        # 加载两帧图像
        img1 = Image.open(first_frame_path)
        img2 = Image.open(second_frame_path)
        
        # 应用变换
        tensor1 = self.transform(img1).unsqueeze(1)  # [C, 1, H, W]
        tensor2 = self.transform(img2).unsqueeze(1)  # [C, 1, H, W]
        
        # 拼接成 [C, 2, H, W]
        combined_tensor = torch.cat([tensor1, tensor2], dim=1)
        
        return combined_tensor
    
    def process_video_directory(self, input_dir, output_dir):
        """处理整个视频目录"""
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 查找所有视频文件
        video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv']
        video_files = []
        
        for ext in video_extensions:
            video_files.extend(glob.glob(os.path.join(input_dir, '**', ext), recursive=True))
        
        print(f"找到 {len(video_files)} 个视频文件")
        
        all_frame_pairs = []
        processed_videos = []
        
        for video_path in tqdm(video_files, desc="处理视频"):
            try:
                # 分析视频
                video_info = self.analyze_video(video_path)
                if video_info is None:
                    continue
                
                # 提取帧对
                frame_pairs = self.extract_frame_pairs(video_path, output_dir)
                all_frame_pairs.extend(frame_pairs)
                
                processed_videos.append({
                    'video_path': video_path,
                    'video_info': video_info,
                    'frame_pairs_count': len(frame_pairs)
                })
                
                print(f"处理完成: {os.path.basename(video_path)} - {len(frame_pairs)} 帧对")
                
            except Exception as e:
                print(f"处理视频时出错 {video_path}: {e}")
                continue
        
        # 保存处理结果的元数据
        metadata = {
            'processed_videos': processed_videos,
            'total_frame_pairs': len(all_frame_pairs),
            'preprocessing_config': {
                'target_size': self.target_size,
                'target_fps': self.target_fps,
                'frame_step': self.frame_step,
                'max_frames': self.max_frames
            }
        }
        
        metadata_path = os.path.join(output_dir, 'preprocessing_metadata.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        # 保存帧对信息
        frame_pairs_path = os.path.join(output_dir, 'frame_pairs.json')
        with open(frame_pairs_path, 'w', encoding='utf-8') as f:
            json.dump(all_frame_pairs, f, indent=2, ensure_ascii=False)
        
        return all_frame_pairs, metadata
    
    def create_laq_dataset_jsonl(self, frame_pairs, output_path):
        """
        创建LAQ推理所需的JSONL格式数据
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            for pair in frame_pairs:
                # 创建LAQ推理格式的数据条目
                entry = {
                    'id': f"{pair['video_name']}_{pair['pair_idx']:05d}",
                    'image': pair['first_frame'],
                    'next_image': pair['second_frame'],
                    'instruction': "Predict the action between these two frames",
                    'vision': "",  # 这里可以添加视觉特征，如果有的话
                    'fields': "[instruction],[vision],delta"
                }
                f.write(json.dumps(entry, ensure_ascii=False) + '\n')
        
        print(f"创建JSONL数据集: {output_path}")
        return output_path

def main():
    parser = argparse.ArgumentParser(description='LAQ视频数据预处理器')
    parser.add_argument('--input_dir', type=str, required=True, help='输入视频目录')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--target_size', type=int, nargs=2, default=[256, 256], help='目标分辨率 [width height]')
    parser.add_argument('--frame_step', type=int, default=5, help='帧间隔')
    parser.add_argument('--max_frames', type=int, default=None, help='每个视频的最大帧数')
    
    args = parser.parse_args()
    
    # 创建预处理器
    preprocessor = LAQVideoPreprocessor(
        target_size=tuple(args.target_size),
        frame_step=args.frame_step,
        max_frames=args.max_frames
    )
    
    # 处理视频
    frame_pairs, metadata = preprocessor.process_video_directory(args.input_dir, args.output_dir)
    
    # 创建JSONL数据集
    jsonl_path = os.path.join(args.output_dir, 'laq_dataset.jsonl')
    preprocessor.create_laq_dataset_jsonl(frame_pairs, jsonl_path)
    
    print(f"\n预处理完成!")
    print(f"总共处理了 {len(metadata['processed_videos'])} 个视频")
    print(f"生成了 {len(frame_pairs)} 个帧对")
    print(f"输出目录: {args.output_dir}")
    print(f"JSONL数据集: {jsonl_path}")

def validate_preprocessing(output_dir):
    """验证预处理结果"""
    print("\n=== 验证预处理结果 ===")

    # 检查元数据文件
    metadata_path = os.path.join(output_dir, 'preprocessing_metadata.json')
    if os.path.exists(metadata_path):
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        print(f"✓ 元数据文件存在: {len(metadata['processed_videos'])} 个视频")
    else:
        print("✗ 元数据文件不存在")
        return False

    # 检查JSONL文件
    jsonl_path = os.path.join(output_dir, 'laq_dataset.jsonl')
    if os.path.exists(jsonl_path):
        with open(jsonl_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        print(f"✓ JSONL数据集存在: {len(lines)} 条记录")
    else:
        print("✗ JSONL数据集不存在")
        return False

    # 验证帧对文件
    frame_pairs_path = os.path.join(output_dir, 'frame_pairs.json')
    if os.path.exists(frame_pairs_path):
        with open(frame_pairs_path, 'r', encoding='utf-8') as f:
            frame_pairs = json.load(f)
        print(f"✓ 帧对文件存在: {len(frame_pairs)} 个帧对")

        # 检查前几个帧对的文件是否存在
        valid_pairs = 0
        for i, pair in enumerate(frame_pairs[:5]):  # 检查前5个
            if os.path.exists(pair['first_frame']) and os.path.exists(pair['second_frame']):
                valid_pairs += 1
        print(f"✓ 前5个帧对文件检查: {valid_pairs}/5 有效")
    else:
        print("✗ 帧对文件不存在")
        return False

    return True

if __name__ == "__main__":
    main()
